import re
import unicodedata
from datetime import datetime
import os
import pandas as pd
from pdf2image import convert_from_path
import numpy as np

# Číselník pro automatickou klasifikaci typů hodnot (value_class)
VALUE_CLASS_REGISTRY = {
    0: "Neznámý typ",
    1: "<PERSON><PERSON>",
    2: "Procenta",
    3: "Desetinné číslo",
    4: "Č<PERSON>lo účtu",
    5: "Alfanumerický kód",
    6: "IBAN",
    7: "DIČ",
    8: "<PERSON><PERSON><PERSON>",
    9: "<PERSON>ěna"
}

# Číselník pro klasifikaci klíčů (key_class) - názvy listů z training_set.xlsx
KEY_CLASS_REGISTRY = {
    1:	 '<PERSON>r<PERSON><PERSON>',
    2:	 '<PERSON><PERSON><PERSON>',
    3:	 '<PERSON><PERSON><PERSON>',
    4:	 'Zálohová faktura',
    5:	 'Dodací list',
    6:	 'Ob<PERSON>dnáv<PERSON>',
    7:	 '<PERSON><PERSON><PERSON> faktury',
    8:	 '<PERSON><PERSON><PERSON> dodac<PERSON>ho listu',
    9:	 '<PERSON><PERSON><PERSON> objednávky',
    10:	 'Variabiln<PERSON> symbol',
    11:	 'Do<PERSON>vate<PERSON>',
    12:	 'O<PERSON><PERSON><PERSON><PERSON><PERSON>',
    13:	 'IČO',
    14:	 'DI<PERSON>',
    15:	 '<PERSON>tum vystavení',
    16:	 'Datum splatnosti',
    17:	 'DUZP',
    18:	 'Číslo účtu',
    19:	 'Kód banky',
    20:	 'IBAN',
    21:	 'Sleva',
    22:	 'Záloha',
    23:	 'Celkem',
    24:	 'Celkem k úhradě',
    25:	 'Celkem s DPH',
    26:	 'Základ DPH',
    27:	 'Sazba DPH',
    28:	 'DPH',
    29:	 'Neplátce DPH',
    30:	 'Rekapitulace DPH',
    31:	 'Zaokrouhlení'
}

# Číselník pro manuální klasifikaci kategorií faktury (result_class)
RESULT_CLASS_REGISTRY = {
    0: "Ostatní",
    1: "Číslo faktury",
    2: "Datum vystavení",
    3: "Datum splatnosti",
    4: "DUZP",
    5: "Číslo objednávky",
    6: "Variabilní symbol",
    7: "DIČ plátce",
    8: "IČO plátce",
    9: "DIČ dodavatele",
    10: "IČO dodavatele",
    11: "IBAN",
    12: "Číslo účtu",
    13: "Kód banky",
    14: "Sazba DPH",
    15: "Základ DPH",
    16: "Částka celkem s DPH",
    17: "Celková částka k úhradě",
    18: "Daň",
    19: "Měna faktury",
    20: "Měna DPH"
}


# Funkce pro práci s číselníky

def get_key_class_id(key_class_name):
    """Vrátí ID pro key_class název."""
    id = KEY_CLASS_REGISTRY.get(key_class_name, 0)
    for key, value in KEY_CLASS_REGISTRY.items():
        if value == key_class_name:
            return key
    return 0  # Výchozí hodnota pro neznámý klíč
def get_value_class_name(value_class_id):
    """Vrátí název pro value_class ID."""
    return VALUE_CLASS_REGISTRY.get(value_class_id, f"Neznámý typ {value_class_id}")


def get_key_class_name(key_class_id):
    """Vrátí název pro key_class ID."""
    return KEY_CLASS_REGISTRY.get(key_class_id, f"Neznámý klíč {key_class_id}")


def get_result_class_name(result_class_id):
    """Vrátí název pro result_class ID."""
    return RESULT_CLASS_REGISTRY.get(result_class_id, f"Neznámá kategorie {result_class_id}")


def get_available_result_classes():
    """Vrátí seznam dostupných kategorií pro dropdown jako (id, název)."""
    return [(class_id, class_name) for class_id, class_name in RESULT_CLASS_REGISTRY.items()]

def get_result_class_registry():
    """Vrátí kopii RESULT_CLASS_REGISTRY."""
    return RESULT_CLASS_REGISTRY.copy()

def load_key_class_mapping_from_xlsx(xlsx_path='training_data/training_set.xlsx'):
    """
    Načte názvy listů z Excel souboru a vytvoří mapování pro Classifier.

    Args:
        xlsx_path (str): Cesta k Excel souboru s tréninkovými daty.

    Returns:
        dict: Mapování názvů kategorií na číselné identifikátory pro Classifier.
    """
    import os

    if not os.path.exists(xlsx_path):
        print(f"Soubor {xlsx_path} nenalezen, používám výchozí mapování.")
        return {}

    try:
        xlsx = pd.ExcelFile(xlsx_path)
        sheet_names = xlsx.sheet_names

        # Vytvoříme mapování název_listu -> ID (začínáme od 1)
        mapping = {}
        for i, sheet_name in enumerate(sheet_names, 2):
            mapping[sheet_name] = i

        print(f"Načteno mapování pro {len(mapping)} kategorií klíčů z {xlsx_path}")
        return mapping

    except Exception as e:
        print(f"Chyba při načítání mapování z {xlsx_path}: {e}")
        return {}


def update_key_class_registry_from_xlsx(xlsx_path='training_data/training_set.xlsx'):
    """
    Aktualizuje KEY_CLASS_REGISTRY na základě názvů listů z Excel souboru.

    Args:
        xlsx_path (str): Cesta k Excel souboru s tréninkovými daty.

    Returns:
        bool: True pokud se aktualizace podařila, jinak False.
    """
    global KEY_CLASS_REGISTRY

    import os

    if not os.path.exists(xlsx_path):
        print(f"Soubor {xlsx_path} nenalezen, KEY_CLASS_REGISTRY zůstává nezměněn.")
        return False

    try:
        xlsx = pd.ExcelFile(xlsx_path)
        sheet_names = xlsx.sheet_names

        # Vytvoříme nový číselník s ID začínajícími od 1
        new_registry = {0: "Ostatní"}  # ID 0 vždy pro "Ostatní"
        for i, sheet_name in enumerate(sheet_names, 1):
            new_registry[i] = sheet_name

        # Aktualizujeme globální číselník
        KEY_CLASS_REGISTRY.clear()
        KEY_CLASS_REGISTRY.update(new_registry)

        print(f"KEY_CLASS_REGISTRY aktualizován s {len(sheet_names)} kategoriemi z {xlsx_path}")
        return True

    except Exception as e:
        print(f"Chyba při aktualizaci KEY_CLASS_REGISTRY z {xlsx_path}: {e}")
        return False

def contains_digits(text):
    return bool(re.search(r'\d', text))

def contains_letters(text):
    return any(unicodedata.category(char).startswith('L') for char in text)

def is_number(text):
    has_digits = contains_digits(text)
    has_punctuation = bool(re.search(r'[-.,]', text))
    return has_digits and has_punctuation

def is_alphanumeric_code(text):
    """Detekuje alfanumerický kód - text obsahující jak písmena, tak číslice"""
    has_letters = contains_letters(text)
    has_digits = contains_digits(text)
    return has_letters and has_digits

def is_alfanumeric_code_with_punctuation(text):
    """Detekuje alfanumerický kód s interpunkčními znaky - text obsahující písmena, číslice a znaky jako lomítko, pomlčka apod."""
    has_letters = contains_letters(text)
    has_digits = contains_digits(text)
    has_punctuation = bool(re.search(r'[/\-_.:\\|~]', text))
    return has_letters and has_digits and has_punctuation

def is_numeric_code_with_punctuation(text):
    """Detekuje numerický kód s interpunkčními znaky - text obsahující písmena, číslice a znaky jako lomítko, pomlčka apod."""
    has_digits = contains_digits(text)
    has_punctuation = bool(re.search(r'[/\-_:\\|~]', text))
    return has_digits and has_punctuation





def clean_texts(df, text_column='text'):
    """
    Čistí texty v DataFrame - nahrazuje měnové symboly a odstraňuje přebytečné mezery.
    NEODSTRAŇUJE nevalidní texty - to se provádí až po klasifikaci pomocí remove_invalid_texts().
    """
    def clean_text(text):
        # Nahrazení měnových symbolů
        text = re.sub(r'Kč', 'CZK', text)
        text = re.sub(r'Ké', 'CZK', text)
        text = re.sub(r'Kě', 'CZK', text)
        text = re.sub(r'Kc', 'CZK', text)
        text = re.sub(r'€', 'EUR', text)
        text = re.sub(r'\$', 'USD', text)
        text = re.sub(r'£', 'GBP', text)
        text = re.sub(r'¥', 'JPY', text)
        text = re.sub(r'Ft', 'HUF', text)
        text = re.sub(r'Fr.', 'CHF', text)
        text = re.sub(r'SFr', 'CHF', text)
        text = re.sub(r'zł', 'PLN', text)

        text = re.sub(r'dph', 'DPH', text)
        text = re.sub(r'Dph', 'DPH', text)
        text = re.sub(r'vat', 'VAT', text)


        text = re.sub(r'[,:;—-‘"+/&<>~¢|®©]', ' ', text)
        text = re.sub(r'\*', ' ', text).strip()
        text = re.sub(r'\(', ' ', text).strip()
        text = re.sub(r'\)', ' ', text).strip()
        text = re.sub(r'\[', ' ', text).strip()
        text = re.sub(r']', ' ', text).strip()
        text = re.sub(r'\\', ' ', text).strip()
        text = re.sub(r'!', ' ', text).strip()
        text = re.sub(r'\?', ' ', text).strip()
        text = re.sub(r'\s+', ' ', text).strip()

        return text.strip()

    # Vytvoříme kopii sloupce pro bezpečnou modifikaci
    cleaned_series = df[text_column].copy()

    # Aplikujeme čištění pouze na řádky s value_class == 0
    mask = df['value_class'] == 0
    cleaned_series[mask] = df.loc[mask, text_column].apply(clean_text)

    # Přiřadíme vyčištěné hodnoty zpět do DataFrame
    df[text_column] = cleaned_series

    print("Text cleaning completed (currency symbols replaced)")

    print("Text cleaning completed (currency symbols replaced)")
    return df


def remove_invalid_texts(df, text_column='text'):
    """
    Odstraňuje nevalidní texty z DataFrame:
    - Texty kratší než 3 znaky (kromě výjimek 'ič', 'ic', 'IČ', 'IC')
    - Texty delší než 40 znaků
    - Pouze z řádků s value_class == 0 (neklasifikované)
    """

    initial_count = len(df)

    # Filtrujeme pouze řádky s value_class == 0 pro odstranění nevalidních textů
    # Řádky s value_class > 0 ponecháváme bez ohledu na délku textu
    df = df[
        (df[text_column].str.contains(r"\d")) |  # Ponecháme všechny texty s číslicí
        (
            (df[text_column].str.len() >= 3) &  # Ponecháme texty >= 3 znaky
            (df[text_column].str.len() <= 40)   # Ponecháme texty <= 40 znaků
        ) |
        (df[text_column].isin(['ič', 'ic', 'IČ', 'IC']))  # Ponecháme výjimky
    ].copy()

    removed_count = initial_count - len(df)
    if removed_count > 0:
        print(f"Odstraněno {removed_count} řádků s nevalidním textem (kratší než 3 nebo delší než 40 znaků, pouze z neklasifikovaných)")
    else:
        print("Žádné nevalidní texty nebyly odstraněny")

    return df

def classify_values(text):
    """Klasifikuje jednotlivý textový řetězec"""
    # 1. Kontrola data
    # r'^(0[1-9]|[12][0-9]|3[01])[-/.](0[1-9]|1[0-2])[-/.](\d{4})$'

    text = re.sub(r'\s+', '', text).strip()

    date_formats = ["%d.%m.%Y", "%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y",
                    "%d.%m.%y", "%y-%m-%d", "%d/%m/%y", "%d-%m-%y"]
    for fmt in date_formats:
        try:
            datetime.strptime(text, fmt)
            return 1  # "Datum"
        except ValueError:
            # Pokud se nepodaří převést text na datum, zkuste další formát
            # if 'datum' in ner:
            #     return 1  # "Datum"
            pass

    # 2. Procenta
    if re.match(r'^\s*\d+([.,]\d+)?\s*%\s*$', text):
        return 2  # "Procenta"

    # 3. Desetinné číslo
    if re.match(r'^\d{1,3}(?:[\s.,]?\d{3})*[.,]\d+$', text):
        return 3  # "Desetinné číslo"

    # 4. Číslo účtu
    if re.match(r'^(?:\d{2,6}-)?\d{3,10}/\d{4}$', text):
        return 4

        # 6. IBAN
    if re.match(r'^[A-Z]{2}[\s-]?(\d{2}[\s-]?([A-Z0-9]{4}[\s-]?){5,7})$', text):
        return 6

    # 7. DIČ
    if re.match(r'^(ATU|BE|BG|CY|CZ|DE|DK|EE|ES|FI|FR|GR|HR|HU|IE|IT|LT|LU|LV|MT|NL|PL|PT|RO|SE|SI|SK)\s?\d{8,12}$',
                text):
        return 7

    # 8. Number
    if re.match(r'^\d{1,20}$', text):
        return 8

        # 5. Alfanumerický kód
    if re.match(r'^(?=.*\d)[A-Za-z0-9./-]+$', text):
        return 5  # "Alfanumerický kód"

    if text in ["CZK", "EUR", "USD", "GBP", "JPY", "HUF", "CHF", "PLN"]:
        return 9  # "Měna"

    return 0  # "Neznámý typ"


def classify_batch_values(df):
    """Zpracuje celý DataFrame a přidá sloupec s klasifikací"""
    df['value_class'] = df['text'].apply(classify_values)

    return df


def get_page_dimensions(file_path):
    """
    Získá rozměry stránky z PDF souboru.

    Args:
        file_path (str): Cesta k PDF souboru

    Returns:
        tuple: (page_width, page_height) v pixelech
    """
    try:
        # Načteme první stránku pro získání rozměrů
        images = convert_from_path(file_path, dpi=300, first_page=1, last_page=1)
        if images:
            image = np.array(images[0])
            height, width = image.shape[:2]
            return width, height
        else:
            raise ValueError("Nelze načíst stránku z PDF")
    except Exception as e:
        print(f"Chyba při získávání rozměrů stránky: {e}")
        # Výchozí rozměry pokud se nepodaří načíst
        return 800, 1200


def ensure_results_directory():
    """Zajistí existenci složky Results."""
    results_dir = "Results"
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
        print(f"Vytvořena složka: {results_dir}")
    return results_dir


def get_output_filename(file_path):
    """
    Vytvoří název výstupního CSV souboru na základě názvu vstupního PDF.

    Args:
        file_path (str): Cesta k vstupnímu PDF souboru

    Returns:
        str: Cesta k výstupnímu CSV souboru ve složce Results
    """
    # Získáme název souboru bez přípony
    base_name = os.path.splitext(os.path.basename(file_path))[0]

    # Zajistíme existenci složky Results
    results_dir = ensure_results_directory()

    # Vytvoříme cestu k výstupnímu souboru
    output_file = os.path.join(results_dir, f"{base_name}.csv")

    return output_file


def postprocess(df):
    if df is None or df.empty:
        return df

    # Zkontrolujeme, zda DataFrame obsahuje potřebné sloupce
    required_columns = ['key_class', 'similarity']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"Varování: DataFrame neobsahuje sloupce: {missing_columns}")
        return df

    # Speciální úprava pro řádek s textem "page"
    page_mask = df['text'] == 'page'
    if page_mask.any():
        df.loc[page_mask, 'key_class'] = 1
        df.loc[page_mask, 'similarity'] = 1.0
        print("Speciální úprava: řádek 'page' nastaven na key_class=1, similarity=1.0")

    # Filtrujeme pouze řádky s key_class > 0
    classified_df = df[df['key_class'] > 0].copy()
    unclassified_df = df[df['key_class'] == 0].copy()

    if classified_df.empty:
        print("Žádné klasifikované řádky k postprocessingu.")
        return df

    print(f"Postprocessing: {len(classified_df)} klasifikovaných řádků")

    # Definujeme třídy s limitem 2 výskyty
    multi_occurrence_classes = {13, 14, 9, 15}  # IČO, DIČ, Číslo objednávky, Datum vystavení

    # Seznam pro výsledné řádky
    filtered_rows = []

    # Seskupíme podle key_class
    grouped = classified_df.groupby('key_class')

    for key_class, group in grouped:
        # Seřadíme podle similarity sestupně
        sorted_group = group.sort_values('similarity', ascending=False)

        # Určíme maximální počet výskytů pro tuto třídu
        if key_class in multi_occurrence_classes:
            max_occurrences = 2
            class_name = get_key_class_name(key_class)
            print(f"  Třída {key_class} ({class_name}): ponechávám max {max_occurrences} výskyty z {len(sorted_group)}")
        else:
            max_occurrences = 1
            class_name = get_key_class_name(key_class)
            print(f"  Třída {key_class} ({class_name}): ponechávám max {max_occurrences} výskyt z {len(sorted_group)}")

        # Ponecháme pouze požadovaný počet nejlepších výskytů
        selected_rows = sorted_group.head(max_occurrences)
        filtered_rows.append(selected_rows)

        # Zobrazíme informace o ponechaných řádcích
        for idx, row in selected_rows.iterrows():
            similarity_rounded = round(row['similarity'], 3)
            print(f"    Ponechán: '{row['text']}' (similarity: {similarity_rounded})")

    # Spojíme všechny filtrované řádky
    if filtered_rows:
        filtered_classified_df = pd.concat(filtered_rows, ignore_index=True)
    else:
        filtered_classified_df = pd.DataFrame(columns=classified_df.columns)

    # Spojíme s neklasifikovanými řádky
    result_df = pd.concat([filtered_classified_df, unclassified_df], ignore_index=True)

    # Seřadíme podle původního pořadí (pokud máme index)
    if 'original_index' in result_df.columns:
        result_df = result_df.sort_values('original_index').drop('original_index', axis=1)


    initial_final_count = len(result_df)
    result_df = result_df[~((result_df['key_class'] == 0) & (result_df['value_class'] == 0))].copy().reset_index(drop=True)
    removed_zero_count = initial_final_count - len(result_df)
    if removed_zero_count > 0:
        print(f"Odstraněno {removed_zero_count} řádků s key_class=0 AND value_class=0")

    removed_count = len(classified_df) - len(filtered_classified_df)
    print(f"Postprocessing dokončen: odstraněno {removed_count} řádků, ponecháno {len(filtered_classified_df)} klasifikovaných řádků")

    return result_df


def export_filtered_results(df, file_path):
    """
    Exportuje filtrované výsledky do CSV souboru.

    Exportují se pouze řádky kde key_class > 0 a result_class > 0.
    Na konec se přidá řádek "page" s bounding boxem celé stránky.

    Args:
        df (pandas.DataFrame): DataFrame s výsledky
        file_path (str): Cesta k původnímu PDF souboru

    Returns:
        str: Cesta k vytvořenému CSV souboru
    """
    # Filtrujeme data podle podmínek
    # filtered_df = df[(df['key_class'] > 0) | (df['result_class'] > 0)].copy()
    #
    # print(f"Filtrování dat:")
    # print(f"  Celkem řádků: {len(df)}")
    # print(f"  key_class > 0: {len(df[df['key_class'] > 0])}")
    # print(f"  result_class > 0: {len(df[df['result_class'] > 0])}")
    # print(f"  Splňuje obě podmínky: {len(filtered_df)}")

    # Získáme název výstupního souboru
    output_file = get_output_filename(file_path)

    # Uložíme do CSV
    df.to_csv(output_file, index=False)

    print(f"Export dokončen:")
    print(f"  Exportováno řádků: {len(df)} (včetně řádku 'page')")
    print(f"  Soubor uložen: {output_file}")

    return output_file


def prepare_gat_data(df):
    """
    Připraví data pro GAT síť z DataFrame po postprocessingu.

    Kroky:
    1. Vypočte souřadnice středu uzlů (cx, cy)
    2. Normalizuje všechny souřadnice podle rozměrů stránky (řádek s textem "page")
    3. Vytvoří one-hot encoding pro key_class a value_class

    Args:
        df (pandas.DataFrame): DataFrame po postprocessingu

    Returns:
        pandas.DataFrame: DataFrame připravený pro GAT síť s novými sloupci
    """
    if df is None or df.empty:
        print("Varování: Prázdný DataFrame pro přípravu GAT dat")
        return df

    # Vytvoříme kopii pro úpravy
    gat_df = df.copy()

    print(f"Příprava GAT dat pro {len(gat_df)} řádků...")

    # 1. Vypočteme souřadnice středu uzlů
    gat_df['cx'] = gat_df['left'] + gat_df['width'] / 2
    gat_df['cy'] = gat_df['top'] + gat_df['height'] / 2
    print("✓ Vypočteny souřadnice středu uzlů (cx, cy)")

    # 2. Najdeme řádek s textem "page" pro normalizaci
    page_row = gat_df[gat_df['text'] == 'page']
    if page_row.empty:
        print("Varování: Řádek 'page' nenalezen, používám výchozí rozměry")
        page_width, page_height = 800, 1200
    else:
        page_width = page_row.iloc[0]['width']
        page_height = page_row.iloc[0]['height']
        print(f"✓ Rozměry stránky: {page_width} x {page_height}")

    # 3. Normalizujeme souřadnice (0-1) - používáme názvy kompatibilní s prepare_graph_data_from_dataframe
    gat_df['norm_left'] = gat_df['left'] / page_width
    gat_df['norm_top'] = gat_df['top'] / page_height
    gat_df['norm_width'] = gat_df['width'] / page_width
    gat_df['norm_height'] = gat_df['height'] / page_height
    gat_df['norm_cx'] = gat_df['cx'] / page_width
    gat_df['norm_cy'] = gat_df['cy'] / page_height
    print("✓ Normalizovány souřadnice")

    # 4. One-hot encoding pro key_class (podle aktuálního číselníku)
    key_class_max = max(KEY_CLASS_REGISTRY.keys())
    for i in range(key_class_max + 1):  # 0-26
        gat_df[f'key_class_{i}'] = (gat_df['key_class'] == i).astype(int)
    print(f"✓ One-hot encoding pro key_class (0-{key_class_max})")

    # 5. One-hot encoding pro value_class (podle aktuálního číselníku)
    value_class_max = max(VALUE_CLASS_REGISTRY.keys())
    for i in range(value_class_max + 1):  # 0-8
        gat_df[f'value_class_{i}'] = (gat_df['value_class'] == i).astype(int)
    print(f"✓ One-hot encoding pro value_class (0-{value_class_max})")

    print(f"GAT data připravena: {len(gat_df)} uzlů s {len(gat_df.columns)} atributy")

    return gat_df
