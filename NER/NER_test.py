import pandas as pd
from gliner import GLiNER

model = GLiNER.from_pretrained("./NER/model")

text = "Číslo faktury: F-2024-001 ze dne: 15.01.2024 da<PERSON>ová rekapitulace v CZK datum splatnosti: 15.02.2024 DUZP: 15.03.2024 celkem k úhradě: 12 128,34 EUR vystavil: Jan <PERSON> IČO:87654321,DIČ:CZ87654321"

# Common NER categories
labels = ["osoba", "organizace", "adresa", "datum", "místo", "telefon", "DIČ", "IČO"]
#labels = ["person", "organization", "location", "date", "subject", "number", "amount", "rate", "currency", "VAT id"]

def ner_predict(df: pd.DataFrame) -> pd.DataFrame:
    if 'text' not in df.columns:
        raise ValueError("DataFrame musí obsahovat sloupec `text`")

    def _clean_text(text):
        if not isinstance(text, str):
            return str(text) if text is not None else ""
        return text

    df['text'] = df['text'].apply(_clean_text)

    df = df.copy()
    def _predict(text):
        try:
            return model.predict_entities(text, labels)
        except Exception:
            return []
    df['ner'] = df['text'].apply(lambda t: _predict(t))

    return df



def ner_predict_row(df: pd.DataFrame, threshold: float = 0.55) -> pd.DataFrame:
    """
    Prochází DataFrame po řádcích (level 4), pro každý řádek spojí slova (level 5)
    do jednoho textu, provede NER, a pokud jsou nalezeny entity, nahradí odpovídající
    slova jedním spojeným tokenem s union bbox a přidá label do sloupce 'NER'.

    Očekávané sloupce v df: ['level','page_num','block_num','par_num','line_num',
    'word_num','left','top','width','height','text'] (aspoň podmnožina), typický
    výstup pytesseract.image_to_data.
    """
    if df is None or df.empty:
        return df

    required_cols = {"level", "left", "top", "width", "height", "text"}
    if not required_cols.issubset(set(df.columns)):
        # Nejsou dostupné nutné sloupce pro výpočet bboxů a textů
        return df

    # Zajistíme sloupec NER
    if "NER" not in df.columns:
        df = df.copy()
        df["NER"] = None
        df["NER_score"] = None
    else:
        df = df.copy()

    # Klíče definující "řádek" (line)
    row_keys = [k for k in ("page_num", "block_num", "par_num", "line_num") if k in df.columns]
    if not row_keys:
        # Pokud nemáme klíče řádku, chovejme se jako kdyby byl celý DF jedna řádka
        groups = [(None, df)]
    else:
        groups = list(df.groupby(row_keys, sort=False))

    drop_indices = set()
    new_rows = []

    def _clean_text_local(t: str) -> str:
        if not isinstance(t, str):
            return str(t) if t is not None else ""
        return t.strip()

    def _build_joined_and_spans(words_df: pd.DataFrame):
        # Vrací (joined_text, spans), kde spans je list dictů: {idx, start, end, text}
        pieces = []
        spans = []
        cursor = 0
        first = True
        for idx, w in words_df.iterrows():
            wt = _clean_text_local(w.get("text", ""))
            if not first:
                pieces.append(" ")
                cursor += 1
            start = cursor
            pieces.append(wt)
            cursor += len(wt)
            end = cursor
            spans.append({"idx": idx, "start": start, "end": end, "text": wt})
            first = False
        joined = "".join(pieces)
        return joined, spans

    def _entities_for_text(text: str):
        try:
            return model.predict_entities(text, labels, threshold=threshold)
        except TypeError:
            # starší signatura bez threshold
            try:
                return model.predict_entities(text, labels)
            except Exception:
                return []
        except Exception:
            return []

    def _map_entity_to_token_indices(entity, spans, joined_text):
        # Preferuj char indexy
        e_start = entity.get("start") if isinstance(entity, dict) else None
        e_end = entity.get("end") if isinstance(entity, dict) else None
        e_text = entity.get("text") if isinstance(entity, dict) else None

        if e_start is None or e_end is None:
            # fallback: pokus o substring match
            if isinstance(e_text, str) and e_text:
                # Hledej první shodu (jednoduché, může být rozšířeno)
                pos = joined_text.find(e_text)
                if pos >= 0:
                    e_start, e_end = pos, pos + len(e_text)
                else:
                    return []
            else:
                return []

        # Najdi tokeny, které se překrývají s entitou
        covered = []
        for s in spans:
            if not (s["end"] <= e_start or s["start"] >= e_end):
                covered.append(s["idx"])
        return covered

    for _, group in groups:
        # V rámci skupiny zpracujeme pouze slova (level==5)
        words = group[group["level"] == 5].copy() if "level" in group.columns else group.copy()
        if words.empty:
            continue
        # Seřaď podle pořadí ve větě (left jako jednoduchá aproximace)
        words = words.sort_values(["left"])  # zachovat stabilně pořadí

        joined_text, spans = _build_joined_and_spans(words)
        if not joined_text:
            continue

        entities = _entities_for_text(joined_text)
        if not entities:
            continue

        # Setřídíme entity podle startu, pokud dostupné, jinak dle délky textu
        def _ent_key(e):
            s = e.get("start") if isinstance(e, dict) else None
            return (0 if isinstance(s, int) else 1, s if isinstance(s, int) else -(len(e.get("text",""))))

        # Filtruj na základě confidence (pokud je k dispozici) a seřaď: nejdřív vyšší conf, pak start
        def _get_conf(e):
            c = e.get("score") or e.get("confidence") or e.get("prob")
            try:
                return float(c)
            except Exception:
                # Pokud není k dispozici, vrátíme None
                return None

        filtered = []
        for e in entities:
            c = _get_conf(e)
            if c is None:
                # pokud model nenavrací confidence, necháme projít vše a spolehneme se na threshold aplikovaný uvnitř predict_entities
                filtered.append((e, None))
            elif c >= threshold:
                filtered.append((e, c))
            # jinak zahodit

        def _sort_key(item):
            e, c = item
            s = e.get("start") if isinstance(e, dict) else None
            # Seřaď podle conf desc (None -> -inf), potom start asc
            conf_key = c if isinstance(c, float) else -1.0
            start_key = s if isinstance(s, int) else 10**9
            return (-conf_key, start_key)

        entities_sorted = [e for e, _ in sorted(filtered, key=_sort_key)]

        used_token_indices = set()
        for ent in entities_sorted:
            label = ent.get("label") if isinstance(ent, dict) else None
            score = _get_conf(ent) if isinstance(ent, dict) else 0
            if not label:
                continue
            covered_indices = _map_entity_to_token_indices(ent, spans, joined_text)
            covered_indices = [i for i in covered_indices if i not in used_token_indices]
            if not covered_indices:
                continue

            # Spojený text pro entitu (z původních tokenů)
            words_subset = df.loc[covered_indices]
            merged_text = " ".join(_clean_text_local(t) for t in words_subset["text"].tolist()).strip()

            # Spočti union bbox
            lefts = words_subset["left"].astype(float).tolist()
            tops = words_subset["top"].astype(float).tolist()
            rights = (words_subset["left"].astype(float) + words_subset["width"].astype(float)).tolist()
            bottoms = (words_subset["top"].astype(float) + words_subset["height"].astype(float)).tolist()

            new_left = float(min(lefts)) if lefts else None
            new_top = float(min(tops)) if tops else None
            new_right = float(max(rights)) if rights else None
            new_bottom = float(max(bottoms)) if bottoms else None
            new_width = None if (new_left is None or new_right is None) else float(new_right - new_left)
            new_height = None if (new_top is None or new_bottom is None) else float(new_bottom - new_top)

            # Základ pro nový řádek - vezmeme první token jako šablonu
            base_row = df.loc[covered_indices[0]].to_dict()
            base_row.update({
                "text": merged_text,
                "left": new_left,
                "top": new_top,
                "width": new_width,
                "height": new_height,
                "NER": label,
                "NER_score": str(score),
                "level": 5,
            })
            # word_num už nedává smysl, můžeme nastavit na min z pokrytých nebo None
            if "word_num" in base_row:
                try:
                    base_row["word_num"] = int(min(df.loc[covered_indices, "word_num"].astype(int)))
                except Exception:
                    base_row["word_num"] = None

            new_rows.append(base_row)
            used_token_indices.update(covered_indices)
            drop_indices.update(covered_indices)

    if not new_rows:
        return df

    # Odstraníme pokryté tokeny a přidáme nové spojené
    df_out = df.drop(index=list(drop_indices), errors="ignore").copy()
    df_out = pd.concat([df_out, pd.DataFrame(new_rows)], ignore_index=True)

    # Volitelné: zachovat přibližné pořadí podle 'top','left'
    if set(["top", "left"]).issubset(set(df_out.columns)):
        try:
            df_out = df_out.sort_values(by=["top", "left", "level"], kind="mergesort").reset_index(drop=True)
        except Exception:
            df_out = df_out.reset_index(drop=True)
    else:
        df_out = df_out.reset_index(drop=True)

    return df_out

if __name__ == "__main__":
    df = pd.DataFrame([text], columns=['text'])
    df = ner_predict(df)
    print(df[['text', 'ner']].to_dict(orient='records'))