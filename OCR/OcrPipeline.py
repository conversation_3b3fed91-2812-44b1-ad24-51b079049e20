import re
import unicodedata
from typing import Optional
import cv2
import pandas as pd

from NER import NER_test
from OCR.OcrEngine import OcrEngine
from OCR.OcrPreview import OcrPreviewQt
from utils.utils import classify_batch_values

CURRENCY_MAP = {
    "Kč": "CZK", "Ké": "CZK", "Kě": "CZK", "Kc": "CZK",
    "€": "EUR", "$": "USD", "£": "GBP", "¥": "JPY",
    "Ft": "HUF", "Fr.": "CHF", "SFr": "CHF", "zł": "PLN",
}
TAX_MAP = {
    "dph": "DPH", "Dph": "DPH",
    "vat": "VAT", "Vat": "VAT",
}
ID_MAP = {
    "D|Č": "DIČ", "DIG": "DIČ", "DIC": "DIČ",
    "Ič": "IČO", "IC": "IČO", "lČ": "IČO",
}

UNWANTED_CHARS = r'[,:;—\-‘"+/&<>~¢|®©\*\[\]()\\!?^]'
NOISE_CHARS = r'[\‘"+&<>~¢®©\*\\!?^]'
INVISIBLE_CHARS = re.compile(r'[\u200B-\u200D\uFEFF]')


class OcrPipeline:
    def __init__(self, lang: str = "ces+eng"):
        self.engine = OcrEngine(lang)

        self.image = None

    def load_image(self, image: cv2.Mat) -> None:
        self.image = image

    def process(self, image: Optional [cv2.Mat]) -> pd.DataFrame:
        """
        Kompletní pipeline: OCR + čištění + spojování.
        """
        if image is not None:
            self.image = image

        df = self.engine.process_data(self.image)
        df = self._clean_text(df)
        df = self._remove_noise(df, confidence_threshold=15.0)
        df = self._normalize_texts(df)
        df = NER_test.ner_predict_row(df, threshold=0.45)
        df = self._merge_words(df, gap_threshold=0.9)
        df = self._remove_non_text(df)
        df = self._split_by_delimiter(df, ocr_engine=self.engine, image=self.image)

        df = self._join_address(df)

        df = classify_batch_values(df)

        preview = OcrPreviewQt(df, self.image)
        preview.show_and_wait()

        return df

    @staticmethod
    def _clean_text(df: pd.DataFrame) -> pd.DataFrame:
        """
        Čistí a normalizuje texty v DataFrame (in-place):
        - Unicode normalizace (NFKC)
        - Odstranění neviditelných znaků
        - Sjednocení vícenásobných mezer na jednu
        - Ořezání whitespace na začátku a konci
        - Odstranění prázdných řádků
        - Reindexace DataFrame
        """
        df["text"] = df["text"].fillna("").astype(str)

        # Unicode normalizace
        df["text"] = df["text"].map(lambda x: unicodedata.normalize("NFKC", x))

        # Odstranění neviditelných znaků (zero-width apod.)
        df["text"] = df["text"].map(lambda x: INVISIBLE_CHARS.sub("", x))

        # Sjednocení vícenásobných whitespace na jednu mezeru
        df["text"] = df["text"].str.replace(r"\s+", " ", regex=True)

        # Ořez whitespace na začátku a konci
        df["text"] = df["text"].str.strip()

        # Odstranění prázdných řádků a reindex
        df = df[df["text"] != ""].reset_index(drop=True)

        return df

    @staticmethod
    def _remove_noise(df: pd.DataFrame, confidence_threshold: float = 25.0) -> pd.DataFrame:
        """
        Odstraní šum z OCR výsledků podle minimálního prahu spolehlivosti, pouze pro řádky kde level == 5.
        """

        # Odstranění šumových znaků
        df["text"] = df["text"].str.replace(NOISE_CHARS, " ", regex=True)
        mask = (df['level'] == 5)
        # Pro level==5 aplikujeme filtr na conf, ostatní ponecháme
        df = df[~mask | (df['conf'] >= confidence_threshold)]
        return df

    @staticmethod
    def _remove_non_text(df: pd.DataFrame) -> pd.DataFrame:
        """
        Odstraní řádky, které neobsahují žádný text (např. obrázky, tabulky, atd.)
        """
        df = df.copy()
        df = df[df['text'].notna() & (df['text'] != '') & (df['text'] != 'nan')]
        return df

    # --- spojování slov ---
    @staticmethod
    def _should_merge_words(cur_text: str, nxt_text: str) -> bool:
        cur_text = cur_text.strip()
        nxt_text = nxt_text.strip()

        # 1. končí : nebo ;
        if cur_text.endswith((':', ';')):
            return False

        # 2. aktuální je číslo a následující obsahuje písmena
        if re.fullmatch(r"\d+", cur_text) and re.search(r"[A-Za-zÁ-ž]", nxt_text):
            return False

        # 3. aktuální nemá číslice a následující obsahuje číslice
        if not re.search(r"\d", cur_text) and re.search(r"\d", nxt_text):
            return False

        # 4. aktuální má číslice a následující neobsahuje číslice
        if re.search(r"\d", cur_text) and not(re.search(r"\d", nxt_text)):
            return False

        return True

    @staticmethod
    def _merge_words(df: pd.DataFrame, gap_threshold: float = 0.5) -> pd.DataFrame:
        """
        Přijme DataFrame z tesseractu (např. výstup pytesseract.image_to_data).
        Spojí všechna slova, která leží na téže řádce (stejné group keys) podle
        pravidel z _should_merge_words a poměru mezery k výšce řádku.
        gap_threshold: poměr mezery k výšce řádku (default 0.8)
        Sloučené texty budou mít level = 5. Conf se ignoruje.
        """
        if df is None or df.empty:
            return pd.DataFrame()

        # určíme klíče pro identifikaci řádků (pokud existují)
        row_keys = [k for k in ("page_num", "block_num", "par_num", "line_num") if k in df.columns]
        if not row_keys:
            # fallback: pokud nejsou tyto sloupce, považuj celý DataFrame za jednu "řádku"
            groups = [("", df.copy())]
        else:
            groups = list(df.groupby(row_keys))

        merged_results: list[dict] = []

        for _, group in groups:
            # filtrujeme pouze slova (level 5), ne řádky (level 4)
            words = group[group["level"] == 5].copy() if "level" in group.columns else group.copy()
            words["text"] = words["text"].fillna("").astype(str)
            words_sorted = words.sort_values(by=["left"]).to_dict(orient="records")

            if not words_sorted:
                continue

            # inicializujeme první slovo
            current = words_sorted[0].copy()
            current["text"] = str(current.get("text", ""))
            current["left"] = int(current.get("left", 0))
            current["top"] = int(current.get("top", 0))
            current["width"] = int(current.get("width", 0))
            current["height"] = int(current.get("height", 0))

            # uchováme původní bbox a flag zda bylo slovo sloučeno
            current_original_bbox = {
                "left": current["left"],
                "top": current["top"],
                "width": current["width"],
                "height": current["height"]
            }
            current_was_merged = False

            for nxt in words_sorted[1:]:
                nxt_entry = nxt.copy()
                nxt_entry["text"] = str(nxt_entry.get("text", ""))
                nxt_entry["left"] = int(nxt_entry.get("left", 0))
                nxt_entry["top"] = int(nxt_entry.get("top", 0))
                nxt_entry["width"] = int(nxt_entry.get("width", 0))
                nxt_entry["height"] = int(nxt_entry.get("height", 0))

                current_x1 = current["left"] + current["width"]
                next_x0 = nxt_entry["left"]
                gap = next_x0 - current_x1

                # výška řádku je maximální výška ze současného a následujícího slova
                line_height = max(current["height"], nxt_entry["height"])
                gap_ratio = gap / line_height if line_height > 0 else float('inf')

                if gap_ratio <= gap_threshold and OcrPipeline._should_merge_words(current["text"], nxt_entry["text"]):
                    # sloučíme
                    new_left = min(current["left"], nxt_entry["left"])
                    new_top = min(current["top"], nxt_entry["top"])
                    new_right = max(current_x1, nxt_entry["left"] + nxt_entry["width"])
                    new_bottom = max(current["top"] + current["height"], nxt_entry["top"] + nxt_entry["height"])

                    # spojení textu se single space, aktualizace bbox
                    current["text"] = (current["text"] + " " + nxt_entry["text"]).strip()
                    current["left"] = new_left
                    current["top"] = new_top
                    current["width"] = new_right - new_left
                    current["height"] = new_bottom - new_top
                    current["level"] = 5  # sloučené texty mají level 5
                    current_was_merged = True  # označíme, že bylo sloučeno
                else:
                    # slova se nespojí - pokud nebylo sloučeno, obnovíme původní bbox
                    if not current_was_merged:
                        current["left"] = current_original_bbox["left"]
                        current["top"] = current_original_bbox["top"]
                        current["width"] = current_original_bbox["width"]
                        current["height"] = current_original_bbox["height"]

                    current["level"] = 5  # sloučené texty mají level 5
                    merged_results.append(current)

                    # začneme nový s původním bbox
                    current = nxt_entry
                    current_original_bbox = {
                        "left": current["left"],
                        "top": current["top"],
                        "width": current["width"],
                        "height": current["height"]
                    }
                    current_was_merged = False  # nové slovo zatím není sloučeno

            # přidáme poslední - pokud nebylo sloučeno, obnovíme původní bbox
            if not current_was_merged:
                current["left"] = current_original_bbox["left"]
                current["top"] = current_original_bbox["top"]
                current["width"] = current_original_bbox["width"]
                current["height"] = current_original_bbox["height"]

            current["level"] = 5  # sloučené texty mají level 5
            merged_results.append(current)

        # převedeme zpět na DataFrame
        if merged_results:
            result_df = pd.DataFrame(merged_results)
            # zachováme původní sloupce, pokud existují
            for col in df.columns:
                if col not in result_df.columns:
                    result_df[col] = None
            return result_df
        else:
            return pd.DataFrame()

    @staticmethod
    def _split_by_delimiter(df: pd.DataFrame, delimiters: Optional[list] = None, ocr_engine: OcrEngine = None,
                            image: cv2.Mat = None) -> pd.DataFrame:
        """
        Rozdělí texty obsahující oddělovač na dva samostatné tokeny.
        Očekává, že ocr_engine.process_boxes(roi) vrátí slovník se seznamy klíčů
        {'char', 'left', 'right', 'top', 'bottom', 'page'}, kde souřadnice jsou
        relativní k ROI. Tesseract uvádí 'top'/'bottom' v souřadnicích s počátkem dole,
        ale pro horizontální dělení potřebujeme pouze 'left'/'right'.
        """
        if df is None or df.empty:
            return df

        if delimiters is None:
            delimiters = ['|', ':', '(', '/']

        # Najdeme kandidáty na dělení (obsahují oddělovač uprostřed textu)
        all_rows_to_split = []
        for delimiter in delimiters:
            delimiter_mask = df['text'].astype(str).str.contains(re.escape(delimiter), na=False)
            potential_rows = df[delimiter_mask].copy()
            for idx, row in potential_rows.iterrows():
                text = str(row['text'])
                if delimiter in text and not text.startswith(delimiter) and not text.endswith(delimiter):
                    # --- SPECIÁLNÍ PRAVIDLO PRO LOMÍTKO ---
                    if delimiter == '/':
                        pos = text.find('/')
                        if pos <= 0 or pos >= len(text) - 1:
                            continue
                        if not (text[pos - 1].isalpha() and text[pos + 1].isalpha()):
                            continue
                    # --- KONEC BLOKU ---

                    parts = text.split(delimiter, 1)
                    if len(parts) == 2 and parts[0].strip() and parts[1].strip():
                        all_rows_to_split.append((idx, row, delimiter))

        if not all_rows_to_split:
            return df

        def _trim_char_list(chars: list[dict]) -> list[dict]:
            # Odstraní úvodní/koncové whitespace znaky, aby bbox odpovídal ořezanému textu
            i, j = 0, len(chars) - 1
            while i <= j and str(chars[i]['char']).isspace():
                i += 1
            while j >= i and str(chars[j]['char']).isspace():
                j -= 1
            return chars[i:j + 1] if i <= j else []

        new_rows = []
        rows_to_remove = []

        for idx, row, delimiter in all_rows_to_split:
            text = str(row['text'])
            parts = text.split(delimiter, 1)
            if len(parts) != 2:
                continue
            before_delimiter = parts[0].strip()
            after_delimiter = parts[1].strip()
            if not before_delimiter or not after_delimiter:
                continue

            try:
                x, y, w, h = int(row['left']), int(row['top']), int(row['width']), int(row['height'])
                if image is None or h <= 0 or w <= 0:
                    continue
                roi = image[y:y + h, x:x + w]

                if ocr_engine is None:
                    # Bez OCR enginu není možné spolehlivě najít pozici oddělovače
                    continue

                boxes = ocr_engine.process_boxes(roi)
                if not boxes or 'char' not in boxes or 'left' not in boxes or 'right' not in boxes:
                    continue

                count = len(boxes['char'])
                if count == 0:
                    continue

                # Sestavíme seznam znaků se souřadnicemi v systému ROI (x zleva, y shora)
                char_positions = []
                for i in range(count):
                    try:
                        ch = boxes['char'][i]
                        left = int(boxes['left'][i])
                        right = int(boxes['right'][i])
                        top_b = int(boxes['top'][i]) if 'top' in boxes else 0
                        bottom_b = int(boxes['bottom'][i]) if 'bottom' in boxes else 0
                        top_tl = max(0, h - top_b)
                        bottom_tl = max(0, h - bottom_b)
                        char_positions.append({'char': ch, 'left': left, 'right': right,
                                               'top': top_tl, 'bottom': bottom_tl})
                    except Exception:
                        continue

                if not char_positions:
                    continue

                detected_text = ''.join([str(c['char']) for c in char_positions])
                delimiter_pos = detected_text.find(delimiter)
                if delimiter_pos == -1:
                    continue

                # Indexy znaků oddělovače v sekvenci
                delimiter_start_idx = delimiter_pos
                delimiter_end_idx = delimiter_pos + len(delimiter) - 1

                chars_before = char_positions[:delimiter_start_idx]
                chars_after = char_positions[delimiter_end_idx + 1:]

                # Ořízneme whitespace, aby bboxy odpovídaly ořezanému textu
                chars_before = _trim_char_list(chars_before)
                chars_after = _trim_char_list(chars_after)

                if not chars_before or not chars_after:
                    continue

                # Vytvoříme nové řádky se zachováním vertikálních rozměrů, měníme pouze X a šířku
                new_row_before = row.copy()
                new_row_before['text'] = ''.join([c['char'] for c in chars_before]).strip()
                left_before_roi = int(chars_before[0]['left'])
                right_before_roi = int(chars_before[-1]['right'])
                new_row_before['left'] = int(x + left_before_roi)
                new_row_before['width'] = int(max(1, right_before_roi - left_before_roi))
                new_row_before['level'] = 6

                new_row_after = row.copy()
                new_row_after['text'] = ''.join([c['char'] for c in chars_after]).strip()
                left_after_roi = int(chars_after[0]['left'])
                right_after_roi = int(chars_after[-1]['right'])
                new_row_after['left'] = int(x + left_after_roi)
                new_row_after['width'] = int(max(1, right_after_roi - left_after_roi))
                new_row_after['level'] = 6

                if new_row_before['text']:
                    new_rows.append(new_row_before)
                if new_row_after['text']:
                    new_rows.append(new_row_after)
                rows_to_remove.append(idx)
            except Exception as e:
                print(f"Error processing row {idx}: {e}")
                continue

        if rows_to_remove:
            df = df.drop(rows_to_remove)
        if new_rows:
            new_df = pd.DataFrame(new_rows)
            df = pd.concat([df, new_df], ignore_index=True)

        return df

    @staticmethod
    def _join_address(
            df: pd.DataFrame,
            tol_line: float = 1.0,  # tolerance pro rozdíl topů (stejný řádek)
            tol_horiz_gap: int = 30,  # max mezera v px mezi fragmenty vedle sebe
            tol_col: float = 2.5,  # tolerance pro zarovnání levé hrany (stejný sloupec)
            tol_vert_gap: float = 1.6  # max vzdálenost mezi řádky (násobek výšky)
    ) -> pd.DataFrame:
        """
        Spojí fragmenty adres/míst, které leží přímo vedle sebe nebo pod sebou.

        - Vedle sebe: stejný řádek (± tol_line * avg_height) a right ≈ left souseda (± tol_horiz_gap)
        - Pod sebou: stejný sloupec (± tol_col * avg_height) a bottom ≈ top souseda (± tol_vert_gap * avg_height)
        Fragmenty se spojují řetězově (hledáme další napravo i pod sebou).
        """

        if df is None or df.empty:
            return df

        # Filtruj řádky s typem NER = adresa nebo místo
        address_mask = df['NER'].astype(str).str.contains(r'(adresa|místo)', case=False, regex=True)
        address_rows = df[address_mask].copy()
        if address_rows.empty:
            return df

        non_address_rows = df[~address_mask].copy()
        avg_height = address_rows['height'].mean()

        # Přidej pomocné sloupce
        address_rows['right'] = address_rows['left'] + address_rows['width']
        address_rows['bottom'] = address_rows['top'] + address_rows['height']

        visited = set()
        merged_addresses = []

        def find_chain(start_idx):
            """Najde celou adresu/místo začínající na start_idx"""
            chain = [start_idx]
            queue = [start_idx]

            while queue:
                cur_idx = queue.pop(0)
                if cur_idx in visited:
                    continue
                visited.add(cur_idx)
                cur = address_rows.loc[cur_idx]

                for idx, row in address_rows.drop(index=visited, errors="ignore").iterrows():
                    # horizontální soused (stejný řádek)
                    same_line = abs(cur['top'] - row['top']) <= avg_height * tol_line
                    horizontal_gap = abs(cur['right'] - row['left'])
                    if same_line and horizontal_gap <= tol_horiz_gap:
                        if idx not in chain:
                            chain.append(idx)
                            queue.append(idx)

                    # vertikální soused (stejný sloupec)
                    same_col = abs(cur['left'] - row['left']) <= avg_height * tol_col
                    vertical_gap = abs(cur['bottom'] - row['top'])
                    if same_col and vertical_gap <= avg_height * tol_vert_gap:
                        if idx not in chain:
                            chain.append(idx)
                            queue.append(idx)

            return chain

        for idx in address_rows.index:
            if idx in visited:
                continue

            cluster_indices = find_chain(idx)
            cluster_rows = address_rows.loc[cluster_indices]

            if len(cluster_rows) == 1:
                merged_addresses.append(cluster_rows.iloc[0])
            else:
                cluster_rows = cluster_rows.sort_values(by=['top', 'left'])

                # Texty – horizontálně mezera, vertikálně newline
                merged_texts = []
                last_top = None
                for _, r in cluster_rows.iterrows():
                    if last_top is not None and abs(r['top'] - last_top) > avg_height * tol_line:
                        merged_texts.append("\n")
                    merged_texts.append(str(r['text']).strip())
                    last_top = r['top']
                merged_text = " ".join(t for t in merged_texts if t).replace(" \n ", "\n")

                # Bounding box
                min_left = cluster_rows["left"].min()
                min_top = cluster_rows["top"].min()
                max_right = cluster_rows["right"].max()
                max_bottom = cluster_rows["bottom"].max()

                merged_row = cluster_rows.iloc[0].copy()
                merged_row['text'] = merged_text.strip()
                merged_row['left'] = min_left
                merged_row['top'] = min_top
                merged_row['width'] = max_right - min_left
                merged_row['height'] = max_bottom - min_top
                merged_row['right'] = max_right
                merged_row['bottom'] = max_bottom

                merged_addresses.append(merged_row)

        merged_df = pd.DataFrame(merged_addresses)
        result_df = pd.concat([non_address_rows, merged_df], ignore_index=True)

        return result_df.sort_values(by=['top', 'left']).reset_index(drop=True).drop(columns=['right', 'bottom'],
                                                                                     errors='ignore')

    @staticmethod
    def _normalize_texts(df: pd.DataFrame) -> pd.DataFrame:
        """
        Normalizuje texty v DataFrame.
        """

        def multi_replace(text: str, replacements: dict) -> str:
            for k, v in replacements.items():
                text = text.replace(k, v)
            return text

        def clean_text(text: str) -> str:
            text = multi_replace(text, CURRENCY_MAP)
            text = multi_replace(text, TAX_MAP)
            text = multi_replace(text, ID_MAP)
            # text = re.sub(UNWANTED_CHARS, ' ', text)
            # text = re.sub(r'\s+', ' ', text).strip()
            return text


        df['text'] = df['text'].apply(clean_text)

        # vyhození prázdných řádků + reindex
        df = df[df["text"] != ""].reset_index(drop=True)

        return df
