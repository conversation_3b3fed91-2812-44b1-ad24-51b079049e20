#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/arcsinh_ops.h>

namespace at {


// aten::arcsinh(Tensor self) -> Tensor
inline at::Tensor arcsinh(const at::Tensor & self) {
    return at::_ops::arcsinh::call(self);
}

// aten::arcsinh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & arcsinh_(at::Tensor & self) {
    return at::_ops::arcsinh_::call(self);
}

// aten::arcsinh.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & arcsinh_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::arcsinh_out::call(self, out);
}
// aten::arcsinh.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & arcsinh_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::arcsinh_out::call(self, out);
}

}
